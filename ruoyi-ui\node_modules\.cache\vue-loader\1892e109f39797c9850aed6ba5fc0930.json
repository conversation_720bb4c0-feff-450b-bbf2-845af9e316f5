{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=style&index=0&id=07a227ce&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753946363828}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRleHRhcmVhLWNvbnRlbnQgew0KICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7DQogIHdvcmQtYnJlYWs6IGJyZWFrLXdvcmQ7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQogIG1heC1oZWlnaHQ6IDEyMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiA4cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCn0NCg0KLnBob25lLW51bWJlciB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouY2hlY2tib3gtY29udGVudCB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/registration-manage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"活动ID\" prop=\"activityId\">\r\n        <el-input\r\n          v-model=\"queryParams.activityId\"\r\n          placeholder=\"请输入活动ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"审核状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择审核状态\" clearable>\r\n          <el-option label=\"待审核\" value=\"0\" />\r\n          <el-option label=\"通过\" value=\"1\" />\r\n          <el-option label=\"拒绝\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:xiqing:registration:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:xiqing:registration:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"registrationManageList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" width=\"80\" />\r\n      <el-table-column label=\"活动标题\" align=\"center\" prop=\"activityTitle\" :show-overflow-tooltip=\"true\" />\r\n\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.status === '0'\" type=\"warning\">待审核</el-tag>\r\n          <el-tag v-else-if=\"scope.row.status === '1'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else-if=\"scope.row.status === '2'\" type=\"danger\">拒绝</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核人\" align=\"center\" prop=\"auditBy\" width=\"100\" />\r\n      <el-table-column label=\"审核时间\" align=\"center\" prop=\"auditTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:query']\"\r\n          >查看</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleAudit(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:audit']\"\r\n            v-if=\"scope.row.status === '0'\"\r\n          >审核</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看报名详情对话框 -->\r\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"报名ID\">{{ form.registrationId }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"活动标题\">{{ form.activityTitle }}</el-descriptions-item>\r\n\r\n        <el-descriptions-item label=\"报名时间\">{{ parseTime(form.registrationTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核状态\">\r\n          <el-tag v-if=\"form.status === '0'\" type=\"warning\">待审核</el-tag>\r\n          <el-tag v-else-if=\"form.status === '1'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else-if=\"form.status === '2'\" type=\"danger\">拒绝</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核人\">{{ form.auditBy }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核时间\">{{ parseTime(form.auditTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核备注\" :span=\"2\">{{ form.auditRemark }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      \r\n      <div style=\"margin-top: 20px;\">\r\n        <h4>报名表单数据：</h4>\r\n        <el-table :data=\"formDataList\" border style=\"margin-top: 10px;\">\r\n          <el-table-column prop=\"key\" label=\"字段名\" width=\"200\" />\r\n          <el-table-column label=\"字段值\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"scope.row.type === 'textarea'\" class=\"textarea-content\">\r\n                {{ scope.row.value }}\r\n              </div>\r\n              <el-tag v-else-if=\"scope.row.type === 'radio' || scope.row.type === 'picker' || scope.row.type === 'select'\"\r\n                      type=\"primary\" size=\"small\">\r\n                {{ scope.row.value }}\r\n              </el-tag>\r\n              <span v-else-if=\"scope.row.type === 'tel' || scope.row.type === 'phone'\"\r\n                    class=\"phone-number\">\r\n                {{ scope.row.value }}\r\n              </span>\r\n              <el-tag v-else-if=\"scope.row.type === 'date'\" type=\"info\" size=\"small\">\r\n                {{ scope.row.value }}\r\n              </el-tag>\r\n              <div v-else-if=\"scope.row.type === 'checkbox'\" class=\"checkbox-content\">\r\n                {{ scope.row.value }}\r\n              </div>\r\n              <div v-else-if=\"scope.row.type === 'file'\" class=\"file-content\">\r\n                <div v-if=\"scope.row.fileList && scope.row.fileList.length > 0\">\r\n                  <div v-for=\"(file, index) in scope.row.fileList\" :key=\"index\" class=\"file-item\">\r\n                    <el-link\r\n                      type=\"primary\"\r\n                      :href=\"file.url\"\r\n                      target=\"_blank\"\r\n                      :download=\"file.name\"\r\n                      class=\"file-link\"\r\n                    >\r\n                      <i class=\"el-icon-document\"></i>\r\n                      {{ file.name }}\r\n                    </el-link>\r\n                  </div>\r\n                </div>\r\n                <span v-else class=\"no-file\">未上传文件</span>\r\n              </div>\r\n              <span v-else>{{ scope.row.value }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog title=\"审核报名\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"auditForm\" :model=\"auditForm\" label-width=\"100px\">\r\n        <el-form-item label=\"审核结果\" prop=\"status\">\r\n          <el-radio-group v-model=\"auditForm.status\">\r\n            <el-radio label=\"1\">通过</el-radio>\r\n            <el-radio label=\"2\">拒绝</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"审核备注\" prop=\"auditRemark\">\r\n          <el-input v-model=\"auditForm.auditRemark\" type=\"textarea\" placeholder=\"请输入审核备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\r\n        <el-button @click=\"auditOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listRegistrationManage, getRegistrationManage, delRegistrationManage, exportRegistrationManage, auditRegistrationManage } from \"@/api/miniapp/xiqing/registration-manage\";\r\nimport { getActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\r\n\r\nexport default {\r\n  name: \"XiqingRegistrationManage\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 西青金种子路演报名管理表格数据\r\n      registrationManageList: [],\r\n      // 是否显示查看弹出层\r\n      viewOpen: false,\r\n      // 是否显示审核弹出层\r\n      auditOpen: false,\r\n      // 表单数据列表\r\n      formDataList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        activityId: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 审核表单参数\r\n      auditForm: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询西青金种子路演报名管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listRegistrationManage(this.queryParams).then(response => {\r\n        this.registrationManageList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.registrationId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      const registrationId = row.registrationId;\r\n      getRegistrationManage(registrationId).then(async response => {\r\n        this.form = response.data;\r\n        await this.parseFormData();\r\n        this.viewOpen = true;\r\n      });\r\n    },\r\n    /** 解析表单数据 */\r\n    async parseFormData() {\r\n      this.formDataList = [];\r\n      if (this.form.formData) {\r\n        try {\r\n          const data = JSON.parse(this.form.formData);\r\n\r\n          // 检查数据格式\r\n          if (Array.isArray(data)) {\r\n            // 新格式：数组格式，每个元素包含name、type、label、value等属性\r\n            data.forEach(field => {\r\n              if (field.name && field.value !== undefined && field.value !== null && field.value !== '') {\r\n                const formDataItem = {\r\n                  key: field.label || field.name, // 优先使用label，没有则使用name\r\n                  value: this.formatFieldValue(field.value, field.type),\r\n                  type: field.type\r\n                };\r\n\r\n                // 如果是文件类型，解析文件列表\r\n                if (field.type === 'file' && field.value) {\r\n                  formDataItem.fileList = this.parseFileList(field.value);\r\n                }\r\n\r\n                this.formDataList.push(formDataItem);\r\n              }\r\n            });\r\n          } else if (typeof data === 'object') {\r\n            // 旧格式：对象格式，key-value形式\r\n            // 获取活动的表单配置来显示正确的字段标签\r\n            const formConfig = await this.getActivityFormConfig();\r\n            const fieldLabelMap = {};\r\n            if (formConfig) {\r\n              formConfig.forEach(field => {\r\n                fieldLabelMap[field.name] = field.label;\r\n              });\r\n            }\r\n\r\n            for (const key in data) {\r\n              if (data[key] !== undefined && data[key] !== null && data[key] !== '') {\r\n                this.formDataList.push({\r\n                  key: fieldLabelMap[key] || key, // 优先使用中文标签，没有则使用原字段名\r\n                  value: data[key],\r\n                  type: 'text'\r\n                });\r\n              }\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单数据失败:', e);\r\n        }\r\n      }\r\n    },\r\n    /** 格式化字段值 */\r\n    formatFieldValue(value, type) {\r\n      if (value === undefined || value === null || value === '') {\r\n        return '未填写';\r\n      }\r\n\r\n      switch (type) {\r\n        case 'checkbox':\r\n          // 复选框类型，value可能是数组\r\n          if (Array.isArray(value)) {\r\n            return value.length > 0 ? value.join(', ') : '未选择';\r\n          }\r\n          return value;\r\n        case 'radio':\r\n        case 'picker':\r\n        case 'select':\r\n          // 单选类型\r\n          return value || '未选择';\r\n        case 'textarea':\r\n          // 文本域类型，保持换行\r\n          return value;\r\n        case 'date':\r\n          // 日期类型\r\n          return value || '未选择';\r\n        case 'tel':\r\n        case 'phone':\r\n          // 电话类型\r\n          return value;\r\n        case 'file':\r\n          // 文件类型，返回原始值，在模板中特殊处理\r\n          return value;\r\n        default:\r\n          // 默认文本类型\r\n          return value;\r\n      }\r\n    },\r\n    /** 解析文件列表 */\r\n    parseFileList(fileValue) {\r\n      if (!fileValue) return [];\r\n\r\n      try {\r\n        // 如果是字符串，尝试解析为JSON\r\n        if (typeof fileValue === 'string') {\r\n          // 可能是JSON字符串\r\n          if (fileValue.startsWith('[') || fileValue.startsWith('{')) {\r\n            const parsed = JSON.parse(fileValue);\r\n            if (Array.isArray(parsed)) {\r\n              return parsed.map(file => ({\r\n                name: file.name || file.fileName || '未知文件',\r\n                url: file.url || file.path || file\r\n              }));\r\n            } else if (parsed.url || parsed.path) {\r\n              return [{\r\n                name: parsed.name || parsed.fileName || '未知文件',\r\n                url: parsed.url || parsed.path\r\n              }];\r\n            }\r\n          } else {\r\n            // 可能是单个文件URL\r\n            return [{\r\n              name: this.getFileNameFromUrl(fileValue),\r\n              url: fileValue\r\n            }];\r\n          }\r\n        }\r\n        // 如果是数组\r\n        else if (Array.isArray(fileValue)) {\r\n          return fileValue.map(file => {\r\n            if (typeof file === 'string') {\r\n              return {\r\n                name: this.getFileNameFromUrl(file),\r\n                url: file\r\n              };\r\n            } else {\r\n              return {\r\n                name: file.name || file.fileName || '未知文件',\r\n                url: file.url || file.path || file\r\n              };\r\n            }\r\n          });\r\n        }\r\n        // 如果是对象\r\n        else if (typeof fileValue === 'object') {\r\n          return [{\r\n            name: fileValue.name || fileValue.fileName || '未知文件',\r\n            url: fileValue.url || fileValue.path || fileValue\r\n          }];\r\n        }\r\n      } catch (e) {\r\n        console.error('解析文件列表失败:', e);\r\n      }\r\n\r\n      return [];\r\n    },\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      const fileName = parts[parts.length - 1];\r\n      return fileName || '未知文件';\r\n    },\r\n    /** 获取活动表单配置 */\r\n    async getActivityFormConfig() {\r\n      if (!this.form.activityId) {\r\n        return null;\r\n      }\r\n      try {\r\n        const response = await getActivityConfig(this.form.activityId);\r\n        if (response.data && response.data.formConfig) {\r\n          return JSON.parse(response.data.formConfig);\r\n        }\r\n      } catch (e) {\r\n        console.error('获取活动表单配置失败:', e);\r\n      }\r\n      return null;\r\n    },\r\n    /** 审核按钮操作 */\r\n    handleAudit(row) {\r\n      this.auditForm = {\r\n        registrationId: row.registrationId,\r\n        status: '1',\r\n        auditRemark: ''\r\n      };\r\n      this.auditOpen = true;\r\n    },\r\n    /** 提交审核 */\r\n    submitAudit() {\r\n      auditRegistrationManage(this.auditForm).then(response => {\r\n        this.$modal.msgSuccess(\"审核成功\");\r\n        this.auditOpen = false;\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const registrationIds = row.registrationId || this.ids;\r\n      this.$modal.confirm('是否确认删除报名编号为\"' + registrationIds + '\"的数据项？').then(function() {\r\n        return delRegistrationManage(registrationIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/xiqing/registration-manage/export', {\r\n        ...this.queryParams\r\n      }, `registration_manage_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.textarea-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.checkbox-content {\r\n  color: #606266;\r\n  line-height: 1.5;\r\n}\r\n</style>\r\n"]}]}